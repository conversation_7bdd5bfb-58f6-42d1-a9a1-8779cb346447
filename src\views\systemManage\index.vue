<template>
  <div class="app-container">
    <!-- 页面标题区域 -->
    <div class="page-header">
      <div class="page-title">
        <i class="el-icon-setting" />
        <span>系统管理</span>
      </div>
      <div class="page-description">管理小程序菜单按钮配置(谨慎操作)</div>
    </div>

    <!-- 功能操作区域 -->
    <div class="action-section">
      <div class="action-card">
        <el-button type="primary" icon="el-icon-plus" @click="showAddDialog">
          新增菜单
        </el-button>
        <el-button type="info" icon="el-icon-refresh" @click="getCornListData" :loading="loading">
          刷新列表
        </el-button>
        <el-button type="success" icon="el-icon-check" @click="batchSave" :loading="saveLoading">
          批量保存
        </el-button>
      </div>
    </div>

    <!-- 小程序菜单列表区域 -->
    <div class="table-section">
      <div class="table-card">
        <div class="table-header">
          <div class="table-title">
            <i class="el-icon-menu" />
            <span>小程序菜单按钮配置</span>
          </div>
          <div class="table-subtitle">共 {{ cornList.length }} 个菜单项</div>
        </div>

        <el-table :data="cornList" stripe style="width: 100%" class="modern-table" :header-cell-style="{
          background: '#f8fafc',
          color: '#374151',
          fontWeight: '600',
        }" :row-style="{ height: '70px' }" v-loading="loading" element-loading-text="加载中...">
          <el-table-column prop="id" label="ID" width="80" align="center">
            <template slot-scope="scope">
              <span class="menu-id">#{{ scope.row.id }}</span>
            </template>
          </el-table-column>

          <el-table-column prop="cornName" label="配置" min-width="200">
            <template slot-scope="scope">
              <el-input v-model="scope.row.cornName" placeholder="请输入配置" size="small"
                @change="markAsChanged(scope.row)" />
            </template>
          </el-table-column>

          <el-table-column prop="cornCode" label="说明" min-width="200">
            <template slot-scope="scope">
              <el-input v-model="scope.row.cornCode" placeholder="请输入说明" size="small"
                @change="markAsChanged(scope.row)" />
            </template>
          </el-table-column>

          <el-table-column prop="status" label="状态" width="200">
            <template slot-scope="scope">
              <el-switch v-model="scope.row.status" :active-value="1" :inactive-value="0" active-color="#13ce66"
                inactive-color="#ff4949" active-text="启用" inactive-text="停用" @change="markAsChanged(scope.row)" />
            </template>
          </el-table-column>

          <el-table-column label="操作" width="300" align="center" fixed="right">
            <template slot-scope="scope">
              <el-button type="primary" size="mini" icon="el-icon-check" @click="updateSingleItem(scope.row)"
                :loading="scope.row.saving" :disabled="!scope.row.changed">
                保存
              </el-button>
              <el-button type="info" size="mini" icon="el-icon-refresh-left" @click="resetItem(scope.row)"
                :disabled="!scope.row.changed">
                重置
              </el-button>
              <el-button type="danger" size="mini" icon="el-icon-delete" @click="deleteItem(scope.row)"
                :loading="scope.row.deleting">
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>

    <!-- 新增菜单弹窗 -->
    <el-dialog title="新增菜单" :visible.sync="addDialogVisible" width="500px" :close-on-click-modal="false">
      <el-form :model="addForm" :rules="addRules" ref="addForm" label-width="100px">
        <el-form-item label="配置" prop="cornName">
          <el-input v-model="addForm.cornName" placeholder="请输入配置" />
        </el-form-item>
        <el-form-item label="说明" prop="cornCode">
          <el-input v-model="addForm.cornCode" placeholder="请输入说明" />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-switch v-model="addForm.status" :active-value="1" :inactive-value="0" active-color="#13ce66"
            inactive-color="#ff4949" active-text="启用" inactive-text="停用" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="addDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmAdd" :loading="addLoading">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getCornList, updateCorn, saveCorn, deleteCorn } from "@/api/systemManage";

export default {
  name: "SystemManage",
  data() {
    return {
      cornList: [],
      originalData: [], // 保存原始数据用于重置
      loading: false,
      saveLoading: false,
      // 新增弹窗相关
      addDialogVisible: false,
      addLoading: false,
      addForm: {
        cornName: '',
        cornCode: '',
        status: 1
      },
      addRules: {
        cornName: [
          { required: true, message: '请输入配置', trigger: 'blur' }
        ],
        cornCode: [
          { required: true, message: '请输入说明', trigger: 'blur' }
        ]
      }
    };
  },
  mounted() {
    this.getCornListData();
  },
  methods: {
    // 获取小程序菜单列表
    async getCornListData() {
      this.loading = true;
      try {
        const { data: response } = await getCornList();

        if (response.code === 200 || response.code === 0) {
          this.cornList = response.data.map((item) => ({
            ...item,
            changed: false,
            saving: false,
            deleting: false,
          }));
          // 深拷贝保存原始数据
          this.originalData = JSON.parse(JSON.stringify(this.cornList));
          // 获取菜单列表成功，不显示消息提示
        } else {
          this.$message.error(response.message || "获取菜单列表失败");
        }
      } catch (error) {
        console.error("获取菜单列表失败:", error);
        this.$message.error("获取菜单列表失败，请检查网络连接");
      } finally {
        this.loading = false;
      }
    },

    // 标记项目已修改
    markAsChanged(item) {
      this.$set(item, "changed", true);
    },

    // 重置单个项目
    resetItem(item) {
      const originalItem = this.originalData.find(
        (orig) => orig.id === item.id
      );
      if (originalItem) {
        Object.assign(item, {
          ...originalItem,
          changed: false,
          saving: false,
        });
        // 重置成功，不显示消息提示
      }
    },

    // 更新单个项目
    async updateSingleItem(item) {
      if (!item.changed) {
        // 该项目未发生变化，不显示消息提示
        return;
      }

      this.$set(item, "saving", true);
      try {
        const updateData = {
          id: item.id,
          cornName: item.cornName,
          cornCode: item.cornCode,
          status: item.status,
        };

        const response = await updateCorn(updateData);
        console.log("更新响应:", response); // 添加日志查看响应结构

        // 修复响应判断逻辑 - 处理嵌套的 data 结构
        const result = response.data || response;
        if (result.code === 200 || result.code === 0) {
          this.$set(item, "changed", false);
          // 更新原始数据
          const originalIndex = this.originalData.findIndex(
            (orig) => orig.id === item.id
          );
          if (originalIndex !== -1) {
            this.originalData[originalIndex] = { ...updateData };
          }
          // 保存成功，不显示消息提示
        } else {
          this.$message.error(result.message || "保存失败");
        }
      } catch (error) {
        console.error("保存失败:", error);
        this.$message.error("保存失败，请检查网络连接");
      } finally {
        this.$set(item, "saving", false);
      }
    },

    // 批量保存所有修改
    async batchSave() {
      const changedItems = this.cornList.filter((item) => item.changed);
      if (changedItems.length === 0) {
        // 没有需要保存的修改，不显示消息提示
        return;
      }

      this.$confirm(
        `确定要保存 ${changedItems.length} 个修改的项目吗？`,
        "确认保存",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }
      ).then(async () => {
        this.saveLoading = true;
        try {
          const promises = changedItems.map((item) => {
            const updateData = {
              id: item.id,
              cornName: item.cornName,
              cornCode: item.cornCode,
              status: item.status,
            };
            return updateCorn(updateData);
          });

          const results = await Promise.all(promises);

          // 修复批量保存的响应判断逻辑
          const successCount = results.filter((response) => {
            const result = response.data || response;
            return result.code === 200 || result.code === 0;
          }).length;

          if (successCount === changedItems.length) {
            // 全部保存成功，更新状态
            changedItems.forEach((item) => {
              this.$set(item, "changed", false);
              // 更新原始数据
              const originalIndex = this.originalData.findIndex(
                (orig) => orig.id === item.id
              );
              if (originalIndex !== -1) {
                this.originalData[originalIndex] = { ...item };
              }
            });
            // 批量保存成功，不显示消息提示
          } else {
            // 部分保存成功，不显示消息提示
          }
        } catch (error) {
          console.error("批量保存失败:", error);
          this.$message.error("批量保存失败，请检查网络连接");
        } finally {
          this.saveLoading = false;
        }
      });
    },

    // 显示新增弹窗
    showAddDialog() {
      this.addDialogVisible = true;
      this.resetAddForm();
    },

    // 重置新增表单
    resetAddForm() {
      this.addForm = {
        cornName: '',
        cornCode: '',
        status: 1
      };
      if (this.$refs.addForm) {
        this.$refs.addForm.clearValidate();
      }
    },

    // 确认新增
    async confirmAdd() {
      this.$refs.addForm.validate(async (valid) => {
        if (valid) {
          this.addLoading = true;
          try {
            const { data: response } = await saveCorn(this.addForm);

            if (response.code === 200 || response.code === 0) {
              this.$message.success('新增菜单成功');
              this.addDialogVisible = false;
              this.resetAddForm();
              // 重新加载列表
              this.getCornListData();
            } else {
              this.$message.error(response.message || '新增菜单失败');
            }
          } catch (error) {
            console.error('新增菜单失败:', error);
            this.$message.error('新增菜单失败，请检查网络连接');
          } finally {
            this.addLoading = false;
          }
        }
      });
    },

    // 删除菜单项
    deleteItem(item) {
      this.$confirm(`确定要删除配置"${item.cornName}"吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        this.$set(item, 'deleting', true);
        try {
          const { data: response } = await deleteCorn(item.id);

          if (response.code === 200 || response.code === 0) {
            this.$message.success('删除成功');
            // 重新加载列表
            this.getCornListData();
          } else {
            this.$message.error(response.message || '删除失败');
          }
        } catch (error) {
          console.error('删除失败:', error);
          this.$message.error('删除失败，请检查网络连接');
        } finally {
          this.$set(item, 'deleting', false);
        }
      }).catch(() => {
        // 用户取消删除
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
  background: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  margin-bottom: 24px;

  .page-title {
    display: flex;
    align-items: center;
    font-size: 28px;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 8px;

    i {
      margin-right: 12px;
      color: #3b82f6;
    }
  }

  .page-description {
    color: #6b7280;
    font-size: 16px;
  }
}

.action-section {
  margin-bottom: 24px;

  .action-card {
    background: white;
    padding: 16px 20px;
    border-radius: 12px;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
    border: 1px solid #e5e7eb;
  }
}

.table-section {
  .table-card {
    background: white;
    border-radius: 12px;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
    border: 1px solid #e5e7eb;
    overflow: hidden;
  }

  .table-header {
    padding: 20px 24px;
    border-bottom: 1px solid #e5e7eb;
    background: #fafbfc;

    .table-title {
      display: flex;
      align-items: center;
      font-size: 20px;
      font-weight: 600;
      color: #1f2937;
      margin-bottom: 4px;

      i {
        margin-right: 8px;
        color: #3b82f6;
      }
    }

    .table-subtitle {
      color: #6b7280;
      font-size: 14px;
    }
  }
}

.modern-table {
  border: none;

  ::v-deep .el-table__body-wrapper {
    .el-table__row {
      transition: all 0.3s ease;

      &:hover {
        background-color: #f8fafc !important;
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
      }
    }
  }

  ::v-deep .el-table__header-wrapper {
    .el-table__header {
      th {
        border-bottom: 2px solid #e5e7eb;
        padding: 16px 0;
      }
    }
  }

  ::v-deep .el-table__body-wrapper {
    .el-table__body {
      td {
        padding: 12px 0;
        border-bottom: 1px solid #f3f4f6;
      }
    }
  }
}

.menu-id {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 4px 8px;

  font-size: 12px;
  font-weight: 600;
}

::v-deep .el-input__inner {
  border: 1px solid #d1d5db;
  transition: all 0.3s ease;

  &:focus {
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  }
}

::v-deep .el-button {
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }
}

::v-deep .el-switch {
  .el-switch__core {
    border-radius: 20px;
  }
}

::v-deep .el-input-number {
  .el-input__inner {
    text-align: center;
  }
}
</style>
